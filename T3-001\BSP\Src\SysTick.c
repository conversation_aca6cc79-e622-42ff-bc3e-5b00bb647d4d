#include "SysTick.h"

volatile uint32_t uwTick = 0;
volatile uint32_t delayTick = 0;

/**
 * @brief systick毫秒设置
 * 
 */
void SysTick_Increasment(void)
{
    uwTick++;
    if (delayTick) delayTick--;
}

/**
 * @brief 获取时基
 * 
 * @return uint32_t uwTick
 */
uint32_t Sys_GetTick(void)
{
    return uwTick;
}

/**
 * @brief 获取时间戳
 * 
 * @param timestamp 
 * @return uint32_t 
 * @note 移植DMP库需要的函数
 */
uint32_t SysGetTick(uint32_t *timestamp)
{
    *timestamp = Sys_GetTick();
    return *timestamp;
}

/**
 * @brief 微秒级延时 (阻塞式，建议使用非阻塞版本)
 *
 * @param xms 延时时间
 */
void Delay(uint32_t xms)
{
    delayTick = xms;
    while (delayTick)
    {
    }
}

/**
 * @brief 启动非阻塞延时
 *
 * @param delay 延时结构体指针
 * @param ms 延时时间(毫秒)
 */
void NonBlockingDelay_Start(NonBlockingDelay_t* delay, uint32_t ms)
{
    if (delay == NULL) return;

    delay->start_tick = Sys_GetTick();
    delay->delay_ms = ms;
    delay->active = true;
}

/**
 * @brief 检查非阻塞延时是否完成
 *
 * @param delay 延时结构体指针
 * @return true 延时完成, false 延时未完成
 */
bool NonBlockingDelay_Check(NonBlockingDelay_t* delay)
{
    if (delay == NULL || !delay->active) return true;

    uint32_t current_tick = Sys_GetTick();

    // 处理时钟溢出情况
    uint32_t elapsed;
    if (current_tick >= delay->start_tick) {
        elapsed = current_tick - delay->start_tick;
    } else {
        // 时钟溢出处理
        elapsed = (UINT32_MAX - delay->start_tick) + current_tick + 1;
    }

    if (elapsed >= delay->delay_ms) {
        delay->active = false;
        return true;
    }

    return false;
}

/**
 * @brief 重置非阻塞延时
 *
 * @param delay 延时结构体指针
 */
void NonBlockingDelay_Reset(NonBlockingDelay_t* delay)
{
    if (delay == NULL) return;

    delay->active = false;
    delay->start_tick = 0;
    delay->delay_ms = 0;
}