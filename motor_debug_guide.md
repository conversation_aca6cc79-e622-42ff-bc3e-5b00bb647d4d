# 电机调试指南

## 问题分析总结

### 原始问题
1. **电机方向乱转** - PID参数过大导致振荡
2. **TB6612发烫** - 频繁换向和过大电流
3. **无规律运动** - 缺乏死区保护和输出限制

### 修复措施

#### 1. PID参数优化
```c
// 原始参数（过大）
PID_IQ_SetParams(&Motor_Left.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);

// 优化后参数（稳定）
PID_IQ_SetParams(&Motor_Left.Motor_PID_Instance, 1.5f, 0.1f, 0.05f);
```

#### 2. 死区保护
- 添加5%的死区，避免小信号时频繁换向
- 输出小于死区时直接停止电机

#### 3. 输出限制
- PID最大输出从100%降低到80%
- 电机任务中额外限制到60%
- 积分项限制从50降低到30

#### 4. 速度滤波
- 添加0.8的滤波系数，避免速度突变
- 最大速度限制200脉冲/秒

#### 5. 安全保护
- 紧急停止函数
- 异常检测和自动保护

## 调试步骤

### 第一步：基础测试
1. 上电后观察OLED显示的电机状态
2. 检查编码器计数是否正常
3. 观察PID输出是否在合理范围内

### 第二步：参数调整
如果仍有问题，可以进一步调整：

```c
// 更保守的PID参数
PID_IQ_SetParams(&Motor_Left.Motor_PID_Instance, 1.0f, 0.05f, 0.02f);

// 更低的目标速度
_iq Data_Motor_TarSpeed = _IQ(10); // 从15降到10

// 更大的死区
const float DEAD_ZONE = 8.0f; // 从5.0f增加到8.0f
```

### 第三步：硬件检查
1. **检查TB6612连接**
   - 确认AIN1/AIN2, BIN1/BIN2连接正确
   - 检查PWM信号是否正常
   - 确认电源电压稳定

2. **检查编码器**
   - 确认编码器A/B相连接正确
   - 检查编码器供电
   - 验证中断触发是否正常

3. **电源检查**
   - 确认电机电源电压合适
   - 检查电流是否过大
   - 确认散热是否充分

## 监控信息

OLED显示内容说明：
- L: 左电机实际速度 -> 目标速度
- R: 右电机实际速度 -> 目标速度  
- Out: 左右电机PID输出
- Enc: 左右编码器计数

正常状态下：
- 实际速度应该逐渐接近目标速度
- PID输出应该在-60到60之间
- 编码器计数应该持续变化

## 紧急处理

如果电机仍然异常，可以调用：
```c
Motor_EmergencyStop(); // 立即停止所有电机
```

或者在Task_App.c中临时注释掉电机任务：
```c
// Task_Add("Motor", Task_Motor_PID, 50, NULL, 1);
```
