#include "Tracker.h"


#define SENSOR_SPACING _IQ(1.5) // 传感器间距，单位：cm

/**
 * @brief 读取灰度传感器数据并计算位置偏差
 * 
 * @param sensor 灰度传感器结构体指针
 * @param tck_data 8路传感器数字量状态数组指针
 * @param offset_ptr 位置偏差值指针 (单位:cm, 负值偏左，正值偏右)
 * @return true 成功计算偏差
 * @return false 未检测到有效轨迹或参数错误
 */

 
bool GrayscaleTracker_Read(No_MCU_Sensor* sensor, uint8_t* tck_data, _iq* offset_ptr)
{
    if (sensor == NULL || tck_data == NULL || offset_ptr == NULL) {
        return false;
    }
    // 获取传感器数字量状态（0表示黑色，1表示白色）
    uint8_t digital = Get_Digtal_For_User(sensor);
    for (uint8_t i = 0; i < 8; i++) {
        tck_data[i] = (digital >> i) & 0x01;
    }

    // 计算位置偏差（加权平均法）
    _iq pos_sum = _IQ(0);
    uint8_t valid_cnt = 0;

    for (uint8_t i = 0; i < 8; i++) {
        // 检测到黑线（根据实际场景调整判断条件）
        if (tck_data[i] == 0) { 
            // 传感器位置权重计算：中心为0，向左右对称分布
            // 8路传感器对应位置：-5.25, -3.75, -2.25, -0.75, 0.75, 2.25, 3.75, 5.25 cm
            _iq sensor_pos = _IQmpy(_IQ(i - 3.5f), SENSOR_SPACING);
            pos_sum += sensor_pos;
            valid_cnt++;
        }
    }

    if (valid_cnt == 0) {
        // 未检测到黑线，返回失败
        return false;
    }

    // 计算平均位置偏差
    *offset_ptr = _IQdiv(pos_sum, _IQ(valid_cnt));
    return true;
}