******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 18:55:53 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007d15


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  0000a1f8  00015e08  R  X
  SRAM                  20200000   00008000  00000715  000078eb  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    0000a1f8   0000a1f8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000089e0   000089e0    r-x .text
  00008aa0    00008aa0    000016e0   000016e0    r-- .rodata
  0000a180    0000a180    00000078   00000078    r-- .cinit
20200000    20200000    00000516   00000000    rw-
  20200000    20200000    000003df   00000000    rw- .bss
  202003e0    202003e0    00000136   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000089e0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001fe8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000021c4    000001b0     Task.o (.text.Task_Start)
                  00002374    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002514    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000026a6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000026a8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002830    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000029b8    0000017c     OLED.o (.text.OLED_Init_NonBlocking)
                  00002b34    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002cac    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002e1c    0000016c     Task_App.o (.text.Task_Motor_PID)
                  00002f88    0000014c     Motor.o (.text.Motor_SetDuty)
                  000030d4    00000144     MPU6050.o (.text.MPU6050_Init)
                  00003218    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00003354    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003488    00000134     libc.a : qsort.c.obj (.text.qsort)
                  000035bc    00000130     OLED.o (.text.OLED_ShowChar)
                  000036ec    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  0000381c    00000128     Task_App.o (.text.Task_Tracker)
                  00003944    00000128     inv_mpu.o (.text.mpu_init)
                  00003a6c    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003b90    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003cb4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003dd4    00000114     Task_App.o (.text.Task_Init)
                  00003ee8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003ff4    00000108     Task_App.o (.text.Task_OLED)
                  000040fc    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00004204    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00004308    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004408    000000f0     Motor.o (.text.Motor_SetDirc)
                  000044f8    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  000045e4    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  000046c8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000047ac    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004890    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000496c    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00004a48    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004b20    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004bf8    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004ccc    000000d0     Motor.o (.text.Motor_GetSpeed)
                  00004d9c    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004e6c    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004f30    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004ff4    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  000050b0    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00005168    000000b4     Motor.o (.text.Motor_SafetyCheck)
                  0000521c    000000b4     Task.o (.text.Task_Add)
                  000052d0    000000ac     Task_App.o (.text.Task_Serial)
                  0000537c    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00005428    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  000054d4    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  0000557e    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00005580    000000a2                            : udivmoddi4.S.obj (.text)
                  00005622    00000002     --HOLE-- [fill = 0]
                  00005624    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000056c4    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00005760    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000057f8    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00005890    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005926    00000002     --HOLE-- [fill = 0]
                  00005928    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  000059b4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005a40    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005acc    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005b50    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005bd4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005c56    00000002     --HOLE-- [fill = 0]
                  00005c58    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005cd4    00000076     SysTick.o (.text.NonBlockingDelay_Check)
                  00005d4a    00000002     --HOLE-- [fill = 0]
                  00005d4c    00000074     Motor.o (.text.Motor_EmergencyStop)
                  00005dc0    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005e34    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005e40    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005eb4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005f28    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00005f9a    00000002     --HOLE-- [fill = 0]
                  00005f9c    00000070     Serial.o (.text.MyPrintf_DMA)
                  0000600c    0000006e     OLED.o (.text.OLED_ShowString)
                  0000607a    00000002     --HOLE-- [fill = 0]
                  0000607c    0000006c     Motor.o (.text.Motor_Start)
                  000060e8    0000006c     Task.o (.text.Task_Delete)
                  00006154    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  000061c0    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000622a    00000002     --HOLE-- [fill = 0]
                  0000622c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00006294    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000062fa    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00006360    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000063c4    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00006428    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000648a    00000002     --HOLE-- [fill = 0]
                  0000648c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000064ee    00000002     --HOLE-- [fill = 0]
                  000064f0    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006550    00000060     Key_Led.o (.text.Key_Read)
                  000065b0    00000060     Task_App.o (.text.Task_IdleFunction)
                  00006610    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006670    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000066d0    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00006730    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000678e    00000002     --HOLE-- [fill = 0]
                  00006790    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000067ec    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00006848    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000068a4    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006900    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006958    00000058     Serial.o (.text.Serial_Init)
                  000069b0    00000058     Task_App.o (.text.Task_OLED_Init)
                  00006a08    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006a60    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006ab8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006b0e    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006b60    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006bb0    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006c00    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006c50    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00006c9c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006ce8    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006d34    0000004c     OLED.o (.text.OLED_Printf)
                  00006d80    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00006dcc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00006e16    00000002     --HOLE-- [fill = 0]
                  00006e18    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006e62    00000002     --HOLE-- [fill = 0]
                  00006e64    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006eac    00000048     ADC.o (.text.adc_getValue)
                  00006ef4    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006f3c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006f84    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006fcc    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00007010    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00007054    00000044     Task_App.o (.text.Task_Key)
                  00007098    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  000070dc    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00007120    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00007164    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000071a6    00000002     --HOLE-- [fill = 0]
                  000071a8    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000071ea    00000002     --HOLE-- [fill = 0]
                  000071ec    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  0000722c    00000040     Interrupt.o (.text.Interrupt_Init)
                  0000726c    00000040     Task_App.o (.text.Task_GraySensor)
                  000072ac    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000072ec    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000732c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  0000736c    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000073ac    0000003e     Task.o (.text.Task_CMP)
                  000073ea    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00007428    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007464    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000074a0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000074dc    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00007518    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00007554    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00007590    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  000075cc    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00007608    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00007644    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00007680    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000076ba    00000002     --HOLE-- [fill = 0]
                  000076bc    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000076f6    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  0000772e    00000002     --HOLE-- [fill = 0]
                  00007730    00000038     Task_App.o (.text.Task_LED)
                  00007768    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000077a0    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000077d4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007808    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000783c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00007870    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  000078a2    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  000078d4    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00007904    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00007934    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007964    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00007994    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000079c4    00000030            : vsnprintf.c.obj (.text._outs)
                  000079f4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007a24    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007a54    0000002c     SysTick.o (.text.NonBlockingDelay_Start)
                  00007a80    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007aac    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007ad8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007b04    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007b30    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00007b5a    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007b82    00000028     OLED.o (.text.DL_Common_updateReg)
                  00007baa    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007bd2    00000002     --HOLE-- [fill = 0]
                  00007bd4    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00007bfc    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007c24    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007c4c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007c74    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007c9c    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007cc4    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007cec    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007d14    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007d3c    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007d62    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007d88    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007dae    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007dd4    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007df8    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00007e1c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00007e40    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007e62    00000002     --HOLE-- [fill = 0]
                  00007e64    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007e84    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007ea4    00000020     SysTick.o (.text.Delay)
                  00007ec4    00000020     main.o (.text.main)
                  00007ee4    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007f04    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007f22    00000002     --HOLE-- [fill = 0]
                  00007f24    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007f42    00000002     --HOLE-- [fill = 0]
                  00007f44    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007f60    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007f7c    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007f98    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007fb4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007fd0    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007fec    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00008008    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00008024    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00008040    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0000805c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00008078    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00008094    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000080b0    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  000080cc    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000080e8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00008104    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00008120    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000813c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00008158    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00008174    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00008190    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000081a8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000081c0    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000081d8    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000081f0    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00008208    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00008220    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00008238    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00008250    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00008268    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00008280    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00008298    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000082b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000082c8    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000082e0    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000082f8    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00008310    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00008328    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00008340    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00008358    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00008370    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00008388    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000083a0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000083b8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000083d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000083e8    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00008400    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00008418    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00008430    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00008448    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00008460    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00008478    00000018     OLED.o (.text.DL_I2C_reset)
                  00008490    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000084a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000084c0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000084d8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000084f0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00008508    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00008520    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00008538    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00008550    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00008568    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00008580    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00008598    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000085b0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000085c8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000085e0    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000085f8    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00008610    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00008628    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00008640    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00008658    00000018            : vsprintf.c.obj (.text._outs)
                  00008670    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00008686    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  0000869c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000086b2    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000086c8    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000086de    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000086f4    00000016     OLED.o (.text.DL_GPIO_readPins)
                  0000870a    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00008720    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00008736    00000016     SysTick.o (.text.SysGetTick)
                  0000874c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00008762    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00008776    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  0000878a    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  0000879e    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000087b2    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000087c6    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000087da    00000002     --HOLE-- [fill = 0]
                  000087dc    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  000087f0    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00008804    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00008818    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000882c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00008840    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00008854    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00008868    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  0000887c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00008890    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  000088a4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000088b8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000088ca    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000088dc    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000088ee    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  000088fe    00000002     --HOLE-- [fill = 0]
                  00008900    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00008910    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00008920    00000010     OLED.o (.text.OLED_IsInitialized)
                  00008930    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00008940    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00008950    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000895e    00000002     --HOLE-- [fill = 0]
                  00008960    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000896e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000897c    0000000e     MPU6050.o (.text.tap_cb)
                  0000898a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00008998    0000000c     SysTick.o (.text.Sys_GetTick)
                  000089a4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000089ae    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000089b8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000089c8    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  000089d2    00000002     --HOLE-- [fill = 0]
                  000089d4    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  000089e4    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  000089ee    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000089f8    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008a02    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00008a0c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00008a1c    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00008a26    0000000a     MPU6050.o (.text.android_orient_cb)
                  00008a30    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008a38    00000008     Interrupt.o (.text.SysTick_Handler)
                  00008a40    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00008a48    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00008a50    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008a56    00000002     --HOLE-- [fill = 0]
                  00008a58    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00008a68    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008a6e    00000006            : exit.c.obj (.text:abort)
                  00008a74    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00008a78    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00008a7c    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00008a80    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00008a84    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00008a94    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00008a98    00000008     --HOLE-- [fill = 0]

.cinit     0    0000a180    00000078     
                  0000a180    0000004e     (.cinit..data.load) [load image, compression = lzss]
                  0000a1ce    00000002     --HOLE-- [fill = 0]
                  0000a1d0    0000000c     (__TI_handler_table)
                  0000a1dc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000a1e4    00000010     (__TI_cinit_table)
                  0000a1f4    00000004     --HOLE-- [fill = 0]

.rodata    0    00008aa0    000016e0     
                  00008aa0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00009696    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009c86    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00009eae    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009eb0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009fb1    00000007     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00009fb8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009ff8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000a020    00000028     inv_mpu.o (.rodata.test)
                  0000a048    0000001f     Task_App.o (.rodata.str1.13166305789289702848.1)
                  0000a067    0000001e     inv_mpu.o (.rodata.reg)
                  0000a085    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  0000a088    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  0000a0a0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000a0b8    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000a0c9    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000a0da    00000011     Task_App.o (.rodata.str1.7950429023856218820.1)
                  0000a0eb    0000000e     Task_App.o (.rodata.str1.3850258909703972507.1)
                  0000a0f9    0000000d     Task_App.o (.rodata.str1.14074990341397557290.1)
                  0000a106    0000000d     Task_App.o (.rodata.str1.5883415095785080416.1)
                  0000a113    00000001     --HOLE-- [fill = 0]
                  0000a114    0000000c     inv_mpu.o (.rodata.hw)
                  0000a120    0000000b     Task_App.o (.rodata.str1.11952760121962574671.1)
                  0000a12b    00000001     --HOLE-- [fill = 0]
                  0000a12c    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000a136    0000000a     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000a140    0000000a     Task_App.o (.rodata.str1.4769078833470683459.1)
                  0000a14a    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000a14c    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  0000a154    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  0000a15c    00000008     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000a164    00000006     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000a16a    00000005     Task_App.o (.rodata.str1.492715258893803702.1)
                  0000a16f    00000004     Task_App.o (.rodata.str1.11683036942922059812.1)
                  0000a173    00000004     Task_App.o (.rodata.str1.8896853068034818020.1)
                  0000a177    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000a179    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003df     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    0000000c     OLED.o (.bss.oled_init_delay)
                  202003bc    00000006     (.common:Data_Accel)
                  202003c2    00000006     (.common:Data_Gyro)
                  202003c8    00000004     (.common:Data_Pitch)
                  202003cc    00000004     (.common:Data_Roll)
                  202003d0    00000004     (.common:Data_Yaw)
                  202003d4    00000004     (.common:ExISR_Flag)
                  202003d8    00000004     (.common:sensor_timestamp)
                  202003dc    00000002     (.common:sensors)
                  202003de    00000001     (.common:more)

.data      0    202003e0    00000136     UNINITIALIZED
                  202003e0    00000048     Motor.o (.data.Motor_Left)
                  20200428    00000048     Motor.o (.data.Motor_Right)
                  20200470    0000002c     inv_mpu.o (.data.st)
                  2020049c    00000010     Task_App.o (.data.Gray_Anolog)
                  202004ac    00000010     Task_App.o (.data.Gray_Normal)
                  202004bc    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004cc    0000000e     MPU6050.o (.data.hal)
                  202004da    00000009     MPU6050.o (.data.gyro_orientation)
                  202004e3    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004eb    00000001     Task_App.o (.data.Flag_LED)
                  202004ec    00000008     Task_App.o (.data.Motor)
                  202004f4    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004f8    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004fc    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200500    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200504    00000004     SysTick.o (.data.delayTick)
                  20200508    00000004     SysTick.o (.data.uwTick)
                  2020050c    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  2020050e    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  2020050f    00000001     Task_App.o (.data.Gray_Digtal)
                  20200510    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200511    00000001     Task.o (.data.Task_Num)
                  20200512    00000001     Task_App.o (.data.Task_OLED_Init.init_started)
                  20200513    00000001     Interrupt.o (.data.enable_group1_irq)
                  20200514    00000001     OLED.o (.data.oled_init_state)
                  20200515    00000001     OLED.o (.data.oled_initialized)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3466    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1788    153       242    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2258    153       248    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2468    0         70     
       OLED_Font.o                      0       2072      0      
       OLED.o                           1978    0         14     
       Motor.o                          1252    0         144    
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Task.o                           782     0         241    
       Serial.o                         404     0         512    
       PID_IQMath.o                     402     0         0      
       SysTick.o                        268     0         8      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           9152    2072      989    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       114       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     35238   6153      1813   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000a1e4 records: 2, size/record: 8, table size: 16
	.data: load addr=0000a180, load size=0000004e bytes, run addr=202003e0, run size=00000136 bytes, compression=lzss
	.bss: load addr=0000a1dc, load size=00000008 bytes, run addr=20200000, run size=000003df bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000a1d0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002515     000089b8     000089b6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   000046c9     000089d4     000089d0   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             000089ec          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008a00          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008a36          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00008a6c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003ee9     00008a0c     00008a0a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000251f     00008a58     00008a54   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008a7e          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007d15     00008a84     00008a80   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00008a75  ADC0_IRQHandler                      
00008a75  ADC1_IRQHandler                      
00008a75  AES_IRQHandler                       
00008a78  C$$EXIT                              
00008a75  CANFD0_IRQHandler                    
00008a75  DAC0_IRQHandler                      
000071ed  DL_ADC12_setClockConfig              
000089a5  DL_Common_delayCycles                
00006c9d  DL_DMA_initChannel                   
00006731  DL_I2C_fillControllerTXFIFO          
000074dd  DL_I2C_flushControllerTXFIFO         
00007daf  DL_I2C_setClockConfig                
00004891  DL_SYSCTL_configSYSPLL               
00006361  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006fcd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004205  DL_Timer_initFourCCPWMMode           
00008121  DL_Timer_setCaptCompUpdateMethod     
00008539  DL_Timer_setCaptureCompareOutCtl     
00008911  DL_Timer_setCaptureCompareValue      
0000813d  DL_Timer_setClockConfig              
00006e65  DL_UART_init                         
000088b9  DL_UART_setClockConfig               
00008a75  DMA_IRQHandler                       
202003bc  Data_Accel                           
202003c2  Data_Gyro                            
202004f4  Data_MotorEncoder                    
202004f8  Data_Motor_TarSpeed                  
202003c8  Data_Pitch                           
202003cc  Data_Roll                            
202004e3  Data_Tracker_Input                   
202004fc  Data_Tracker_Offset                  
202003d0  Data_Yaw                             
00008a75  Default_Handler                      
00007ea5  Delay                                
202003d4  ExISR_Flag                           
202004eb  Flag_LED                             
2020050e  Flag_MPU6050_Ready                   
00008a75  GROUP0_IRQHandler                    
000045e5  GROUP1_IRQHandler                    
0000496d  Get_Analog_value                     
00007555  Get_Anolog_Value                     
00008951  Get_Digtal_For_User                  
000076f7  Get_Normalize_For_User               
202002f0  GraySensor                           
2020049c  Gray_Anolog                          
2020050f  Gray_Digtal                          
202004ac  Gray_Normal                          
00008a79  HOSTexit                             
00008a75  HardFault_Handler                    
00008a75  I2C0_IRQHandler                      
00008a75  I2C1_IRQHandler                      
000061c1  I2C_OLED_Clear                       
00007591  I2C_OLED_Set_Pos                     
00005761  I2C_OLED_WR_Byte                     
000064f1  I2C_OLED_i2c_sda_unlock              
0000722d  Interrupt_Init                       
00006551  Key_Read                             
000030d5  MPU6050_Init                         
202004ec  Motor                                
00005d4d  Motor_EmergencyStop                  
00004ccd  Motor_GetSpeed                       
202003e0  Motor_Left                           
20200428  Motor_Right                          
00005169  Motor_SafetyCheck                    
00002f89  Motor_SetDuty                        
0000607d  Motor_Start                          
00005f9d  MyPrintf_DMA                         
00008a75  NMI_Handler                          
000026a9  No_MCU_Ganv_Sensor_Init              
00005f29  No_MCU_Ganv_Sensor_Init_Frist        
00007165  No_Mcu_Ganv_Sensor_Task_Without_tick 
00005cd5  NonBlockingDelay_Check               
00007a55  NonBlockingDelay_Start               
000029b9  OLED_Init_NonBlocking                
00008921  OLED_IsInitialized                   
00006d35  OLED_Printf                          
000035bd  OLED_ShowChar                        
0000600d  OLED_ShowString                      
00007b31  PID_IQ_Init                          
00003a6d  PID_IQ_Prosc                         
00007011  PID_IQ_SetParams                     
00008a75  PendSV_Handler                       
00008a75  RTC_IRQHandler                       
0000159d  Read_Quad                            
00008a81  Reset_Handler                        
00008a75  SPI0_IRQHandler                      
00008a75  SPI1_IRQHandler                      
00008a75  SVC_Handler                          
00006d81  SYSCFG_DL_ADC1_init                  
00007935  SYSCFG_DL_DMA_CH_RX_init             
000085f9  SYSCFG_DL_DMA_CH_TX_init             
00005e35  SYSCFG_DL_DMA_init                   
00001e09  SYSCFG_DL_GPIO_init                  
00006901  SYSCFG_DL_I2C_MPU6050_init           
000063c5  SYSCFG_DL_I2C_OLED_init              
00005929  SYSCFG_DL_Motor_PWM_init             
00006791  SYSCFG_DL_SYSCTL_init                
00008931  SYSCFG_DL_SYSTICK_init               
00005acd  SYSCFG_DL_UART0_init                 
00007a81  SYSCFG_DL_init                       
00005625  SYSCFG_DL_initPower                  
00006959  Serial_Init                          
20200000  Serial_RxData                        
00008737  SysGetTick                           
00008a39  SysTick_Handler                      
00007cc5  SysTick_Increasment                  
00008999  Sys_GetTick                          
00008a75  TIMA0_IRQHandler                     
00008a75  TIMA1_IRQHandler                     
00008a75  TIMG0_IRQHandler                     
00008a75  TIMG12_IRQHandler                    
00008a75  TIMG6_IRQHandler                     
00008a75  TIMG7_IRQHandler                     
00008a75  TIMG8_IRQHandler                     
000088cb  TI_memcpy_small                      
0000898b  TI_memset_small                      
0000521d  Task_Add                             
000060e9  Task_Delete                          
0000726d  Task_GraySensor                      
000065b1  Task_IdleFunction                    
00003dd5  Task_Init                            
00007055  Task_Key                             
00007731  Task_LED                             
00002e1d  Task_Motor_PID                       
00003ff5  Task_OLED                            
000069b1  Task_OLED_Init                       
000052d1  Task_Serial                          
000021c5  Task_Start                           
0000381d  Task_Tracker                         
00008a75  UART0_IRQHandler                     
00008a75  UART1_IRQHandler                     
00008a75  UART2_IRQHandler                     
00008a75  UART3_IRQHandler                     
00008611  _IQ24div                             
00008629  _IQ24mpy                             
00007965  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000a1e4  __TI_CINIT_Base                      
0000a1f4  __TI_CINIT_Limit                     
0000a1f4  __TI_CINIT_Warm                      
0000a1d0  __TI_Handler_Table_Base              
0000a1dc  __TI_Handler_Table_Limit             
00007645  __TI_auto_init_nobinit_nopinit       
00005c59  __TI_decompress_lzss                 
000088dd  __TI_decompress_none                 
00006a09  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000874d  __TI_zero_init_nomemset              
0000251f  __adddf3                             
00004b2b  __addsf3                             
00009eb0  __aeabi_ctype_table_                 
00009eb0  __aeabi_ctype_table_C                
00005e41  __aeabi_d2f                          
00006e19  __aeabi_d2iz                         
000071a9  __aeabi_d2uiz                        
0000251f  __aeabi_dadd                         
00006429  __aeabi_dcmpeq                       
00006465  __aeabi_dcmpge                       
00006479  __aeabi_dcmpgt                       
00006451  __aeabi_dcmple                       
0000643d  __aeabi_dcmplt                       
00003ee9  __aeabi_ddiv                         
000046c9  __aeabi_dmul                         
00002515  __aeabi_dsub                         
20200500  __aeabi_errno                        
00008a41  __aeabi_errno_addr                   
000072ed  __aeabi_f2d                          
00007769  __aeabi_f2iz                         
00004b2b  __aeabi_fadd                         
0000648d  __aeabi_fcmpeq                       
000064c9  __aeabi_fcmpge                       
000064dd  __aeabi_fcmpgt                       
000064b5  __aeabi_fcmple                       
000064a1  __aeabi_fcmplt                       
00005bd5  __aeabi_fdiv                         
000059b5  __aeabi_fmul                         
00004b21  __aeabi_fsub                         
00007ad9  __aeabi_i2d                          
000075cd  __aeabi_i2f                          
00006ab9  __aeabi_idiv                         
000026a7  __aeabi_idiv0                        
00006ab9  __aeabi_idivmod                      
0000557f  __aeabi_ldiv0                        
00007f25  __aeabi_llsl                         
00007e1d  __aeabi_lmul                         
00008a49  __aeabi_memcpy                       
00008a49  __aeabi_memcpy4                      
00008a49  __aeabi_memcpy8                      
00008961  __aeabi_memset                       
00008961  __aeabi_memset4                      
00008961  __aeabi_memset8                      
00007df9  __aeabi_ui2d                         
00007ced  __aeabi_ui2f                         
000072ad  __aeabi_uidiv                        
000072ad  __aeabi_uidivmod                     
00008869  __aeabi_uldivmod                     
00007f25  __ashldi3                            
ffffffff  __binit__                            
0000622d  __cmpdf2                             
00007681  __cmpsf2                             
00003ee9  __divdf3                             
00005bd5  __divsf3                             
0000622d  __eqdf2                              
00007681  __eqsf2                              
000072ed  __extendsfdf2                        
00006e19  __fixdfsi                            
00007769  __fixsfsi                            
000071a9  __fixunsdfsi                         
00007ad9  __floatsidf                          
000075cd  __floatsisf                          
00007df9  __floatunsidf                        
00007ced  __floatunsisf                        
00005dc1  __gedf2                              
00007609  __gesf2                              
00005dc1  __gtdf2                              
00007609  __gtsf2                              
0000622d  __ledf2                              
00007681  __lesf2                              
0000622d  __ltdf2                              
00007681  __ltsf2                              
UNDEFED   __mpu_init                           
000046c9  __muldf3                             
00007e1d  __muldi3                             
000076bd  __muldsi3                            
000059b5  __mulsf3                             
0000622d  __nedf2                              
00007681  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002515  __subdf3                             
00004b21  __subsf3                             
00005e41  __truncdfsf2                         
00005581  __udivmoddi4                         
00007d15  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00008a95  _system_pre_init                     
00008a6f  abort                                
00006ead  adc_getValue                         
00009c86  asc2_0806                            
00009696  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002831  atan2                                
00002831  atan2l                               
00000df5  atanl                                
0000732d  atoi                                 
ffffffff  binit                                
00006155  convertAnalogToDigital               
20200504  delayTick                            
00006ef5  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00006611  dmp_enable_gyro_cal                  
00006f3d  dmp_enable_lp_quat                   
00008175  dmp_load_motion_driver_firmware      
00001c15  dmp_read_fifo                        
0000887d  dmp_register_android_orient_cb       
00008891  dmp_register_tap_cb                  
000057f9  dmp_set_fifo_rate                    
00002b35  dmp_set_orientation                  
00007099  dmp_set_shake_reject_thresh          
00007871  dmp_set_shake_reject_time            
000078a3  dmp_set_shake_reject_timeout         
000062fb  dmp_set_tap_axes                     
000070dd  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
000079f5  dmp_set_tap_time                     
00007a25  dmp_set_tap_time_multi               
20200513  enable_group1_irq                    
000067ed  frexp                                
000067ed  frexpl                               
0000a114  hw                                   
00000000  interruptVectors                     
00004a49  ldexp                                
00004a49  ldexpl                               
00007ec5  main                                 
00007e41  memccpy                              
00007ee5  memcmp                               
202003de  more                                 
00006671  mpu6050_i2c_sda_unlock               
00004ff5  mpu_configure_fifo                   
00005eb5  mpu_get_accel_fsr                    
000066d1  mpu_get_gyro_fsr                     
0000783d  mpu_get_sample_rate                  
00003945  mpu_init                             
00003b91  mpu_load_firmware                    
00004309  mpu_lp_accel_mode                    
000040fd  mpu_read_fifo_stream                 
0000537d  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
000047ad  mpu_set_accel_fsr                    
00002375  mpu_set_bypass                       
000050b1  mpu_set_dmp_state                    
00004e6d  mpu_set_gyro_fsr                     
000056c5  mpu_set_int_latched                  
00004d9d  mpu_set_lpf                          
000044f9  mpu_set_sample_rate                  
000036ed  mpu_set_sensors                      
00005429  mpu_write_mem                        
00003355  mspm0_i2c_read                       
00004f31  mspm0_i2c_write                      
000054d5  normalizeAnalogValues                
00003489  qsort                                
202003a0  quat                                 
0000a067  reg                                  
00004a49  scalbn                               
00004a49  scalbnl                              
202003d8  sensor_timestamp                     
202003dc  sensors                              
00002cad  sqrt                                 
00002cad  sqrtl                                
0000a020  test                                 
20200508  uwTick                               
0000736d  vsnprintf                            
00007b05  vsprintf                             
00008941  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  dmp_read_fifo                        
00001e09  SYSCFG_DL_GPIO_init                  
000021c5  Task_Start                           
00002375  mpu_set_bypass                       
00002515  __aeabi_dsub                         
00002515  __subdf3                             
0000251f  __adddf3                             
0000251f  __aeabi_dadd                         
000026a7  __aeabi_idiv0                        
000026a9  No_MCU_Ganv_Sensor_Init              
00002831  atan2                                
00002831  atan2l                               
000029b9  OLED_Init_NonBlocking                
00002b35  dmp_set_orientation                  
00002cad  sqrt                                 
00002cad  sqrtl                                
00002e1d  Task_Motor_PID                       
00002f89  Motor_SetDuty                        
000030d5  MPU6050_Init                         
00003355  mspm0_i2c_read                       
00003489  qsort                                
000035bd  OLED_ShowChar                        
000036ed  mpu_set_sensors                      
0000381d  Task_Tracker                         
00003945  mpu_init                             
00003a6d  PID_IQ_Prosc                         
00003b91  mpu_load_firmware                    
00003dd5  Task_Init                            
00003ee9  __aeabi_ddiv                         
00003ee9  __divdf3                             
00003ff5  Task_OLED                            
000040fd  mpu_read_fifo_stream                 
00004205  DL_Timer_initFourCCPWMMode           
00004309  mpu_lp_accel_mode                    
000044f9  mpu_set_sample_rate                  
000045e5  GROUP1_IRQHandler                    
000046c9  __aeabi_dmul                         
000046c9  __muldf3                             
000047ad  mpu_set_accel_fsr                    
00004891  DL_SYSCTL_configSYSPLL               
0000496d  Get_Analog_value                     
00004a49  ldexp                                
00004a49  ldexpl                               
00004a49  scalbn                               
00004a49  scalbnl                              
00004b21  __aeabi_fsub                         
00004b21  __subsf3                             
00004b2b  __addsf3                             
00004b2b  __aeabi_fadd                         
00004ccd  Motor_GetSpeed                       
00004d9d  mpu_set_lpf                          
00004e6d  mpu_set_gyro_fsr                     
00004f31  mspm0_i2c_write                      
00004ff5  mpu_configure_fifo                   
000050b1  mpu_set_dmp_state                    
00005169  Motor_SafetyCheck                    
0000521d  Task_Add                             
000052d1  Task_Serial                          
0000537d  mpu_read_mem                         
00005429  mpu_write_mem                        
000054d5  normalizeAnalogValues                
0000557f  __aeabi_ldiv0                        
00005581  __udivmoddi4                         
00005625  SYSCFG_DL_initPower                  
000056c5  mpu_set_int_latched                  
00005761  I2C_OLED_WR_Byte                     
000057f9  dmp_set_fifo_rate                    
00005929  SYSCFG_DL_Motor_PWM_init             
000059b5  __aeabi_fmul                         
000059b5  __mulsf3                             
00005acd  SYSCFG_DL_UART0_init                 
00005bd5  __aeabi_fdiv                         
00005bd5  __divsf3                             
00005c59  __TI_decompress_lzss                 
00005cd5  NonBlockingDelay_Check               
00005d4d  Motor_EmergencyStop                  
00005dc1  __gedf2                              
00005dc1  __gtdf2                              
00005e35  SYSCFG_DL_DMA_init                   
00005e41  __aeabi_d2f                          
00005e41  __truncdfsf2                         
00005eb5  mpu_get_accel_fsr                    
00005f29  No_MCU_Ganv_Sensor_Init_Frist        
00005f9d  MyPrintf_DMA                         
0000600d  OLED_ShowString                      
0000607d  Motor_Start                          
000060e9  Task_Delete                          
00006155  convertAnalogToDigital               
000061c1  I2C_OLED_Clear                       
0000622d  __cmpdf2                             
0000622d  __eqdf2                              
0000622d  __ledf2                              
0000622d  __ltdf2                              
0000622d  __nedf2                              
000062fb  dmp_set_tap_axes                     
00006361  DL_SYSCTL_setHFCLKSourceHFXTParams   
000063c5  SYSCFG_DL_I2C_OLED_init              
00006429  __aeabi_dcmpeq                       
0000643d  __aeabi_dcmplt                       
00006451  __aeabi_dcmple                       
00006465  __aeabi_dcmpge                       
00006479  __aeabi_dcmpgt                       
0000648d  __aeabi_fcmpeq                       
000064a1  __aeabi_fcmplt                       
000064b5  __aeabi_fcmple                       
000064c9  __aeabi_fcmpge                       
000064dd  __aeabi_fcmpgt                       
000064f1  I2C_OLED_i2c_sda_unlock              
00006551  Key_Read                             
000065b1  Task_IdleFunction                    
00006611  dmp_enable_gyro_cal                  
00006671  mpu6050_i2c_sda_unlock               
000066d1  mpu_get_gyro_fsr                     
00006731  DL_I2C_fillControllerTXFIFO          
00006791  SYSCFG_DL_SYSCTL_init                
000067ed  frexp                                
000067ed  frexpl                               
00006901  SYSCFG_DL_I2C_MPU6050_init           
00006959  Serial_Init                          
000069b1  Task_OLED_Init                       
00006a09  __TI_ltoa                            
00006ab9  __aeabi_idiv                         
00006ab9  __aeabi_idivmod                      
00006c9d  DL_DMA_initChannel                   
00006d35  OLED_Printf                          
00006d81  SYSCFG_DL_ADC1_init                  
00006e19  __aeabi_d2iz                         
00006e19  __fixdfsi                            
00006e65  DL_UART_init                         
00006ead  adc_getValue                         
00006ef5  dmp_enable_6x_lp_quat                
00006f3d  dmp_enable_lp_quat                   
00006fcd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00007011  PID_IQ_SetParams                     
00007055  Task_Key                             
00007099  dmp_set_shake_reject_thresh          
000070dd  dmp_set_tap_count                    
00007165  No_Mcu_Ganv_Sensor_Task_Without_tick 
000071a9  __aeabi_d2uiz                        
000071a9  __fixunsdfsi                         
000071ed  DL_ADC12_setClockConfig              
0000722d  Interrupt_Init                       
0000726d  Task_GraySensor                      
000072ad  __aeabi_uidiv                        
000072ad  __aeabi_uidivmod                     
000072ed  __aeabi_f2d                          
000072ed  __extendsfdf2                        
0000732d  atoi                                 
0000736d  vsnprintf                            
000074dd  DL_I2C_flushControllerTXFIFO         
00007555  Get_Anolog_Value                     
00007591  I2C_OLED_Set_Pos                     
000075cd  __aeabi_i2f                          
000075cd  __floatsisf                          
00007609  __gesf2                              
00007609  __gtsf2                              
00007645  __TI_auto_init_nobinit_nopinit       
00007681  __cmpsf2                             
00007681  __eqsf2                              
00007681  __lesf2                              
00007681  __ltsf2                              
00007681  __nesf2                              
000076bd  __muldsi3                            
000076f7  Get_Normalize_For_User               
00007731  Task_LED                             
00007769  __aeabi_f2iz                         
00007769  __fixsfsi                            
0000783d  mpu_get_sample_rate                  
00007871  dmp_set_shake_reject_time            
000078a3  dmp_set_shake_reject_timeout         
00007935  SYSCFG_DL_DMA_CH_RX_init             
00007965  _IQ24toF                             
000079f5  dmp_set_tap_time                     
00007a25  dmp_set_tap_time_multi               
00007a55  NonBlockingDelay_Start               
00007a81  SYSCFG_DL_init                       
00007ad9  __aeabi_i2d                          
00007ad9  __floatsidf                          
00007b05  vsprintf                             
00007b31  PID_IQ_Init                          
00007cc5  SysTick_Increasment                  
00007ced  __aeabi_ui2f                         
00007ced  __floatunsisf                        
00007d15  _c_int00_noargs                      
00007daf  DL_I2C_setClockConfig                
00007df9  __aeabi_ui2d                         
00007df9  __floatunsidf                        
00007e1d  __aeabi_lmul                         
00007e1d  __muldi3                             
00007e41  memccpy                              
00007ea5  Delay                                
00007ec5  main                                 
00007ee5  memcmp                               
00007f25  __aeabi_llsl                         
00007f25  __ashldi3                            
00008121  DL_Timer_setCaptCompUpdateMethod     
0000813d  DL_Timer_setClockConfig              
00008175  dmp_load_motion_driver_firmware      
00008539  DL_Timer_setCaptureCompareOutCtl     
000085f9  SYSCFG_DL_DMA_CH_TX_init             
00008611  _IQ24div                             
00008629  _IQ24mpy                             
00008737  SysGetTick                           
0000874d  __TI_zero_init_nomemset              
00008869  __aeabi_uldivmod                     
0000887d  dmp_register_android_orient_cb       
00008891  dmp_register_tap_cb                  
000088b9  DL_UART_setClockConfig               
000088cb  TI_memcpy_small                      
000088dd  __TI_decompress_none                 
00008911  DL_Timer_setCaptureCompareValue      
00008921  OLED_IsInitialized                   
00008931  SYSCFG_DL_SYSTICK_init               
00008941  wcslen                               
00008951  Get_Digtal_For_User                  
00008961  __aeabi_memset                       
00008961  __aeabi_memset4                      
00008961  __aeabi_memset8                      
0000898b  TI_memset_small                      
00008999  Sys_GetTick                          
000089a5  DL_Common_delayCycles                
00008a39  SysTick_Handler                      
00008a41  __aeabi_errno_addr                   
00008a49  __aeabi_memcpy                       
00008a49  __aeabi_memcpy4                      
00008a49  __aeabi_memcpy8                      
00008a6f  abort                                
00008a75  ADC0_IRQHandler                      
00008a75  ADC1_IRQHandler                      
00008a75  AES_IRQHandler                       
00008a75  CANFD0_IRQHandler                    
00008a75  DAC0_IRQHandler                      
00008a75  DMA_IRQHandler                       
00008a75  Default_Handler                      
00008a75  GROUP0_IRQHandler                    
00008a75  HardFault_Handler                    
00008a75  I2C0_IRQHandler                      
00008a75  I2C1_IRQHandler                      
00008a75  NMI_Handler                          
00008a75  PendSV_Handler                       
00008a75  RTC_IRQHandler                       
00008a75  SPI0_IRQHandler                      
00008a75  SPI1_IRQHandler                      
00008a75  SVC_Handler                          
00008a75  TIMA0_IRQHandler                     
00008a75  TIMA1_IRQHandler                     
00008a75  TIMG0_IRQHandler                     
00008a75  TIMG12_IRQHandler                    
00008a75  TIMG6_IRQHandler                     
00008a75  TIMG7_IRQHandler                     
00008a75  TIMG8_IRQHandler                     
00008a75  UART0_IRQHandler                     
00008a75  UART1_IRQHandler                     
00008a75  UART2_IRQHandler                     
00008a75  UART3_IRQHandler                     
00008a78  C$$EXIT                              
00008a79  HOSTexit                             
00008a81  Reset_Handler                        
00008a95  _system_pre_init                     
00009696  asc2_1608                            
00009c86  asc2_0806                            
00009eb0  __aeabi_ctype_table_                 
00009eb0  __aeabi_ctype_table_C                
0000a020  test                                 
0000a067  reg                                  
0000a114  hw                                   
0000a1d0  __TI_Handler_Table_Base              
0000a1dc  __TI_Handler_Table_Limit             
0000a1e4  __TI_CINIT_Base                      
0000a1f4  __TI_CINIT_Limit                     
0000a1f4  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003bc  Data_Accel                           
202003c2  Data_Gyro                            
202003c8  Data_Pitch                           
202003cc  Data_Roll                            
202003d0  Data_Yaw                             
202003d4  ExISR_Flag                           
202003d8  sensor_timestamp                     
202003dc  sensors                              
202003de  more                                 
202003e0  Motor_Left                           
20200428  Motor_Right                          
2020049c  Gray_Anolog                          
202004ac  Gray_Normal                          
202004e3  Data_Tracker_Input                   
202004eb  Flag_LED                             
202004ec  Motor                                
202004f4  Data_MotorEncoder                    
202004f8  Data_Motor_TarSpeed                  
202004fc  Data_Tracker_Offset                  
20200500  __aeabi_errno                        
20200504  delayTick                            
20200508  uwTick                               
2020050e  Flag_MPU6050_Ready                   
2020050f  Gray_Digtal                          
20200513  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[333 symbols]
