******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 18:29:16 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007a45


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009f28  000160d8  R  X
  SRAM                  20200000   00008000  00000715  000078eb  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009f28   00009f28    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008700   00008700    r-x .text
  000087c0    000087c0    000016f0   000016f0    r-- .rodata
  00009eb0    00009eb0    00000078   00000078    r-- .cinit
20200000    20200000    00000516   00000000    rw-
  20200000    20200000    000003df   00000000    rw- .bss
  202003e0    202003e0    00000136   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008700     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001fe8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000021c4    000001b0     Task.o (.text.Task_Start)
                  00002374    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002514    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000026a6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000026a8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002830    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000029b8    0000017c     OLED.o (.text.OLED_Init_NonBlocking)
                  00002b34    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002cac    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002e1c    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002f60    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000309c    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000031d0    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003304    00000130     OLED.o (.text.OLED_ShowChar)
                  00003434    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003564    00000128     Task_App.o (.text.Task_Tracker)
                  0000368c    00000128     inv_mpu.o (.text.mpu_init)
                  000037b4    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  000038d8    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  000039fc    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003b1c    00000114     Task_App.o (.text.Task_Init)
                  00003c30    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003d3c    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003e44    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003f48    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004048    000000f0     Motor.o (.text.Motor_SetDirc)
                  00004138    000000f0     Task_App.o (.text.Task_Motor_PID)
                  00004228    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004314    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  000043f8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000044dc    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  000045c0    000000e0     Task_App.o (.text.Task_OLED)
                  000046a0    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000477c    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00004858    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004930    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004a08    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004adc    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004bac    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004c70    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004d34    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004df0    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004ea8    000000b4     Task.o (.text.Task_Add)
                  00004f5c    000000ac     Task_App.o (.text.Task_Serial)
                  00005008    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  000050b4    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00005160    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  0000520a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000520c    000000a2                            : udivmoddi4.S.obj (.text)
                  000052ae    00000002     --HOLE-- [fill = 0]
                  000052b0    000000a0     Motor.o (.text.Motor_SetDuty)
                  00005350    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000053f0    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  0000548c    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00005524    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000055bc    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005652    00000002     --HOLE-- [fill = 0]
                  00005654    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  000056e0    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000576c    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  000057f8    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  0000587c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005900    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005982    00000002     --HOLE-- [fill = 0]
                  00005984    00000080     Motor.o (.text.Motor_GetSpeed)
                  00005a04    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005a80    00000076     SysTick.o (.text.NonBlockingDelay_Check)
                  00005af6    00000002     --HOLE-- [fill = 0]
                  00005af8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005b6c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005b70    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005be4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005c58    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00005cca    00000002     --HOLE-- [fill = 0]
                  00005ccc    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005d3c    0000006e     OLED.o (.text.OLED_ShowString)
                  00005daa    00000002     --HOLE-- [fill = 0]
                  00005dac    0000006c     Motor.o (.text.Motor_Start)
                  00005e18    0000006c     Task.o (.text.Task_Delete)
                  00005e84    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00005ef0    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005f5a    00000002     --HOLE-- [fill = 0]
                  00005f5c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005fc4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000602a    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00006090    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000060f4    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00006158    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000061ba    00000002     --HOLE-- [fill = 0]
                  000061bc    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000621e    00000002     --HOLE-- [fill = 0]
                  00006220    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006280    00000060     Key_Led.o (.text.Key_Read)
                  000062e0    00000060     Task_App.o (.text.Task_IdleFunction)
                  00006340    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  000063a0    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00006400    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00006460    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000064be    00000002     --HOLE-- [fill = 0]
                  000064c0    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000651c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00006578    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000065d4    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006630    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006688    00000058     Serial.o (.text.Serial_Init)
                  000066e0    00000058     Task_App.o (.text.Task_OLED_Init)
                  00006738    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006790    00000058            : _printfi.c.obj (.text._pconv_f)
                  000067e8    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000683e    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006890    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000068e0    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006930    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006980    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000069cc    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006a18    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006a64    0000004c     OLED.o (.text.OLED_Printf)
                  00006ab0    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00006afc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00006b46    00000002     --HOLE-- [fill = 0]
                  00006b48    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006b92    00000002     --HOLE-- [fill = 0]
                  00006b94    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006bdc    00000048     ADC.o (.text.adc_getValue)
                  00006c24    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006c6c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006cb4    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006cfc    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006d40    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006d84    00000044     Task_App.o (.text.Task_Key)
                  00006dc8    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006e0c    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006e50    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006e94    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00006ed6    00000002     --HOLE-- [fill = 0]
                  00006ed8    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00006f1a    00000002     --HOLE-- [fill = 0]
                  00006f1c    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00006f5c    00000040     Interrupt.o (.text.Interrupt_Init)
                  00006f9c    00000040     Task_App.o (.text.Task_GraySensor)
                  00006fdc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000701c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000705c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  0000709c    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000070dc    0000003e     Task.o (.text.Task_CMP)
                  0000711a    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00007158    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007194    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000071d0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000720c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00007248    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00007284    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  000072c0    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  000072fc    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00007338    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00007374    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000073b0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000073ea    00000002     --HOLE-- [fill = 0]
                  000073ec    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00007426    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  0000745e    00000002     --HOLE-- [fill = 0]
                  00007460    00000038     Task_App.o (.text.Task_LED)
                  00007498    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000074d0    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007504    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007538    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000756c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  000075a0    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  000075d2    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00007604    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00007634    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00007664    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007694    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000076c4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000076f4    00000030            : vsnprintf.c.obj (.text._outs)
                  00007724    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007754    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007784    0000002c     SysTick.o (.text.NonBlockingDelay_Start)
                  000077b0    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000077dc    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007808    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007834    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007860    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  0000788a    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  000078b2    00000028     OLED.o (.text.DL_Common_updateReg)
                  000078da    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007902    00000002     --HOLE-- [fill = 0]
                  00007904    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  0000792c    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007954    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  0000797c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000079a4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000079cc    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000079f4    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007a1c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007a44    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007a6c    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007a92    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007ab8    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007ade    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007b04    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007b28    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00007b4c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00007b70    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007b92    00000002     --HOLE-- [fill = 0]
                  00007b94    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007bb4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007bd4    00000020     SysTick.o (.text.Delay)
                  00007bf4    00000020     main.o (.text.main)
                  00007c14    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007c34    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007c52    00000002     --HOLE-- [fill = 0]
                  00007c54    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007c72    00000002     --HOLE-- [fill = 0]
                  00007c74    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007c90    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007cac    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007cc8    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007ce4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007d00    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007d1c    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007d38    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007d54    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007d70    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007d8c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007da8    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007dc4    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007de0    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007dfc    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007e18    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007e34    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007e50    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007e6c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007e88    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00007ea4    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007ec0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00007ed8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00007ef0    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007f08    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007f20    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007f38    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007f50    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007f68    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007f80    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007f98    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007fb0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007fc8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007fe0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007ff8    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00008010    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00008028    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00008040    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00008058    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00008070    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00008088    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000080a0    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  000080b8    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000080d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000080e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00008100    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00008118    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00008130    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00008148    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00008160    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00008178    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00008190    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000081a8    00000018     OLED.o (.text.DL_I2C_reset)
                  000081c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000081d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000081f0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00008208    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00008220    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00008238    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00008250    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00008268    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00008280    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00008298    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000082b0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000082c8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000082e0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000082f8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00008310    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00008328    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00008340    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00008358    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00008370    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00008388    00000018            : vsprintf.c.obj (.text._outs)
                  000083a0    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  000083b6    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  000083cc    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000083e2    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000083f8    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000840e    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00008424    00000016     OLED.o (.text.DL_GPIO_readPins)
                  0000843a    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00008450    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00008466    00000016     SysTick.o (.text.SysGetTick)
                  0000847c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00008492    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  000084a6    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000084ba    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  000084ce    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000084e2    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000084f6    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000850a    00000002     --HOLE-- [fill = 0]
                  0000850c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00008520    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00008534    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00008548    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000855c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00008570    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00008584    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00008598    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000085ac    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  000085c0    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  000085d4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000085e8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000085fa    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000860c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000861e    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  0000862e    00000002     --HOLE-- [fill = 0]
                  00008630    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00008640    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00008650    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00008660    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00008670    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000867e    00000002     --HOLE-- [fill = 0]
                  00008680    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000868e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000869c    0000000e     MPU6050.o (.text.tap_cb)
                  000086aa    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  000086b8    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000086c4    0000000c     SysTick.o (.text.Sys_GetTick)
                  000086d0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000086da    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000086e4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000086f4    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  000086fe    00000002     --HOLE-- [fill = 0]
                  00008700    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008710    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000871a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008724    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000872e    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00008738    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00008748    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00008752    0000000a     MPU6050.o (.text.android_orient_cb)
                  0000875c    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008764    00000008     Interrupt.o (.text.SysTick_Handler)
                  0000876c    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00008774    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000877c    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008782    00000002     --HOLE-- [fill = 0]
                  00008784    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00008794    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000879a    00000006            : exit.c.obj (.text:abort)
                  000087a0    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000087a4    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000087a8    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000087ac    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000087bc    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00009eb0    00000078     
                  00009eb0    0000004d     (.cinit..data.load) [load image, compression = lzss]
                  00009efd    00000003     --HOLE-- [fill = 0]
                  00009f00    0000000c     (__TI_handler_table)
                  00009f0c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009f14    00000010     (__TI_cinit_table)
                  00009f24    00000004     --HOLE-- [fill = 0]

.rodata    0    000087c0    000016f0     
                  000087c0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000093b6    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  000099a6    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00009bce    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009bd0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009cd1    00000007     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00009cd8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009d18    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009d40    00000028     inv_mpu.o (.rodata.test)
                  00009d68    0000001f     Task_App.o (.rodata.str1.13166305789289702848.1)
                  00009d87    0000001e     inv_mpu.o (.rodata.reg)
                  00009da5    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00009da8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009dc0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009dd8    00000014     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00009dec    00000014     Task_App.o (.rodata.str1.3850258909703972507.1)
                  00009e00    00000014     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00009e14    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00009e25    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00009e36    00000011     Task_App.o (.rodata.str1.7950429023856218820.1)
                  00009e47    00000001     --HOLE-- [fill = 0]
                  00009e48    0000000c     inv_mpu.o (.rodata.hw)
                  00009e54    0000000c     Task_App.o (.rodata.str1.4769078833470683459.1)
                  00009e60    0000000b     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009e6b    00000001     --HOLE-- [fill = 0]
                  00009e6c    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00009e76    0000000a     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00009e80    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00009e88    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00009e90    00000008     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009e98    00000006     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00009e9e    00000005     Task_App.o (.rodata.str1.492715258893803702.1)
                  00009ea3    00000004     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00009ea7    00000004     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009eab    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009ead    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00009eaf    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003df     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    0000000c     OLED.o (.bss.oled_init_delay)
                  202003bc    00000006     (.common:Data_Accel)
                  202003c2    00000006     (.common:Data_Gyro)
                  202003c8    00000004     (.common:Data_Pitch)
                  202003cc    00000004     (.common:Data_Roll)
                  202003d0    00000004     (.common:Data_Yaw)
                  202003d4    00000004     (.common:ExISR_Flag)
                  202003d8    00000004     (.common:sensor_timestamp)
                  202003dc    00000002     (.common:sensors)
                  202003de    00000001     (.common:more)

.data      0    202003e0    00000136     UNINITIALIZED
                  202003e0    00000048     Motor.o (.data.Motor_Left)
                  20200428    00000048     Motor.o (.data.Motor_Right)
                  20200470    0000002c     inv_mpu.o (.data.st)
                  2020049c    00000010     Task_App.o (.data.Gray_Anolog)
                  202004ac    00000010     Task_App.o (.data.Gray_Normal)
                  202004bc    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004cc    0000000e     MPU6050.o (.data.hal)
                  202004da    00000009     MPU6050.o (.data.gyro_orientation)
                  202004e3    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004eb    00000001     Task_App.o (.data.Flag_LED)
                  202004ec    00000008     Task_App.o (.data.Motor)
                  202004f4    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004f8    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004fc    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  20200500    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200504    00000004     SysTick.o (.data.delayTick)
                  20200508    00000004     SysTick.o (.data.uwTick)
                  2020050c    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  2020050e    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  2020050f    00000001     Task_App.o (.data.Gray_Digtal)
                  20200510    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200511    00000001     Task.o (.data.Task_Num)
                  20200512    00000001     Task_App.o (.data.Task_OLED_Init.init_started)
                  20200513    00000001     Interrupt.o (.data.enable_group1_irq)
                  20200514    00000001     OLED.o (.data.oled_init_state)
                  20200515    00000001     OLED.o (.data.oled_initialized)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3466    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1624    175       242    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2094    175       248    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2468    0         70     
       OLED_Font.o                      0       2072      0      
       OLED.o                           1962    0         14     
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Task.o                           782     0         241    
       Serial.o                         404     0         512    
       Motor.o                          704     0         144    
       PID_IQMath.o                     402     0         0      
       SysTick.o                        268     0         8      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8588    2072      989    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       113       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     34510   6174      1813   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009f14 records: 2, size/record: 8, table size: 16
	.data: load addr=00009eb0, load size=0000004d bytes, run addr=202003e0, run size=00000136 bytes, compression=lzss
	.bss: load addr=00009f0c, load size=00000008 bytes, run addr=20200000, run size=000003df bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009f00 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002515     000086e4     000086e2   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   000043f9     00008700     000086fc   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008718          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000872c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008762          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00008798          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003c31     00008738     00008736   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000251f     00008784     00008780   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000087a6          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007a45     000087ac     000087a8   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00005b6d  ADC0_IRQHandler                      
00005b6d  ADC1_IRQHandler                      
00005b6d  AES_IRQHandler                       
000087a0  C$$EXIT                              
00005b6d  CANFD0_IRQHandler                    
00005b6d  DAC0_IRQHandler                      
00006f1d  DL_ADC12_setClockConfig              
000086d1  DL_Common_delayCycles                
000069cd  DL_DMA_initChannel                   
00006461  DL_I2C_fillControllerTXFIFO          
0000720d  DL_I2C_flushControllerTXFIFO         
00007adf  DL_I2C_setClockConfig                
000046a1  DL_SYSCTL_configSYSPLL               
00006091  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006cfd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003e45  DL_Timer_initFourCCPWMMode           
00007e51  DL_Timer_setCaptCompUpdateMethod     
00008269  DL_Timer_setCaptureCompareOutCtl     
00008641  DL_Timer_setCaptureCompareValue      
00007e6d  DL_Timer_setClockConfig              
00006b95  DL_UART_init                         
000085e9  DL_UART_setClockConfig               
00005b6d  DMA_IRQHandler                       
202003bc  Data_Accel                           
202003c2  Data_Gyro                            
202004f4  Data_MotorEncoder                    
202004f8  Data_Motor_TarSpeed                  
202003c8  Data_Pitch                           
202003cc  Data_Roll                            
202004e3  Data_Tracker_Input                   
202004fc  Data_Tracker_Offset                  
202003d0  Data_Yaw                             
00005b6d  Default_Handler                      
00007bd5  Delay                                
202003d4  ExISR_Flag                           
202004eb  Flag_LED                             
2020050e  Flag_MPU6050_Ready                   
00005b6d  GROUP0_IRQHandler                    
00004315  GROUP1_IRQHandler                    
0000477d  Get_Analog_value                     
00007285  Get_Anolog_Value                     
00008671  Get_Digtal_For_User                  
00007427  Get_Normalize_For_User               
202002f0  GraySensor                           
2020049c  Gray_Anolog                          
2020050f  Gray_Digtal                          
202004ac  Gray_Normal                          
000087a1  HOSTexit                             
00005b6d  HardFault_Handler                    
00005b6d  I2C0_IRQHandler                      
00005b6d  I2C1_IRQHandler                      
00005ef1  I2C_OLED_Clear                       
000072c1  I2C_OLED_Set_Pos                     
0000548d  I2C_OLED_WR_Byte                     
00006221  I2C_OLED_i2c_sda_unlock              
00006f5d  Interrupt_Init                       
00006281  Key_Read                             
00002e1d  MPU6050_Init                         
202004ec  Motor                                
00005985  Motor_GetSpeed                       
202003e0  Motor_Left                           
20200428  Motor_Right                          
000052b1  Motor_SetDuty                        
00005dad  Motor_Start                          
00005ccd  MyPrintf_DMA                         
00005b6d  NMI_Handler                          
000026a9  No_MCU_Ganv_Sensor_Init              
00005c59  No_MCU_Ganv_Sensor_Init_Frist        
00006e95  No_Mcu_Ganv_Sensor_Task_Without_tick 
00005a81  NonBlockingDelay_Check               
00007785  NonBlockingDelay_Start               
000029b9  OLED_Init_NonBlocking                
00006a65  OLED_Printf                          
00003305  OLED_ShowChar                        
00005d3d  OLED_ShowString                      
00007861  PID_IQ_Init                          
000037b5  PID_IQ_Prosc                         
00006d41  PID_IQ_SetParams                     
00005b6d  PendSV_Handler                       
00005b6d  RTC_IRQHandler                       
0000159d  Read_Quad                            
000087a9  Reset_Handler                        
00005b6d  SPI0_IRQHandler                      
00005b6d  SPI1_IRQHandler                      
00005b6d  SVC_Handler                          
00006ab1  SYSCFG_DL_ADC1_init                  
00007665  SYSCFG_DL_DMA_CH_RX_init             
00008329  SYSCFG_DL_DMA_CH_TX_init             
000086b9  SYSCFG_DL_DMA_init                   
00001e09  SYSCFG_DL_GPIO_init                  
00006631  SYSCFG_DL_I2C_MPU6050_init           
000060f5  SYSCFG_DL_I2C_OLED_init              
00005655  SYSCFG_DL_Motor_PWM_init             
000064c1  SYSCFG_DL_SYSCTL_init                
00008651  SYSCFG_DL_SYSTICK_init               
000057f9  SYSCFG_DL_UART0_init                 
000077b1  SYSCFG_DL_init                       
00005351  SYSCFG_DL_initPower                  
00006689  Serial_Init                          
20200000  Serial_RxData                        
00008467  SysGetTick                           
00008765  SysTick_Handler                      
000079f5  SysTick_Increasment                  
000086c5  Sys_GetTick                          
00005b6d  TIMA0_IRQHandler                     
00005b6d  TIMA1_IRQHandler                     
00005b6d  TIMG0_IRQHandler                     
00005b6d  TIMG12_IRQHandler                    
00005b6d  TIMG6_IRQHandler                     
00005b6d  TIMG7_IRQHandler                     
00005b6d  TIMG8_IRQHandler                     
000085fb  TI_memcpy_small                      
000086ab  TI_memset_small                      
00004ea9  Task_Add                             
00005e19  Task_Delete                          
00006f9d  Task_GraySensor                      
000062e1  Task_IdleFunction                    
00003b1d  Task_Init                            
00006d85  Task_Key                             
00007461  Task_LED                             
00004139  Task_Motor_PID                       
000045c1  Task_OLED                            
000066e1  Task_OLED_Init                       
00004f5d  Task_Serial                          
000021c5  Task_Start                           
00003565  Task_Tracker                         
00005b6d  UART0_IRQHandler                     
00005b6d  UART1_IRQHandler                     
00005b6d  UART2_IRQHandler                     
00005b6d  UART3_IRQHandler                     
00008341  _IQ24div                             
00008359  _IQ24mpy                             
00007695  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009f14  __TI_CINIT_Base                      
00009f24  __TI_CINIT_Limit                     
00009f24  __TI_CINIT_Warm                      
00009f00  __TI_Handler_Table_Base              
00009f0c  __TI_Handler_Table_Limit             
00007375  __TI_auto_init_nobinit_nopinit       
00005a05  __TI_decompress_lzss                 
0000860d  __TI_decompress_none                 
00006739  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000847d  __TI_zero_init_nomemset              
0000251f  __adddf3                             
0000493b  __addsf3                             
00009bd0  __aeabi_ctype_table_                 
00009bd0  __aeabi_ctype_table_C                
00005b71  __aeabi_d2f                          
00006b49  __aeabi_d2iz                         
00006ed9  __aeabi_d2uiz                        
0000251f  __aeabi_dadd                         
00006159  __aeabi_dcmpeq                       
00006195  __aeabi_dcmpge                       
000061a9  __aeabi_dcmpgt                       
00006181  __aeabi_dcmple                       
0000616d  __aeabi_dcmplt                       
00003c31  __aeabi_ddiv                         
000043f9  __aeabi_dmul                         
00002515  __aeabi_dsub                         
20200500  __aeabi_errno                        
0000876d  __aeabi_errno_addr                   
0000701d  __aeabi_f2d                          
00007499  __aeabi_f2iz                         
0000493b  __aeabi_fadd                         
000061bd  __aeabi_fcmpeq                       
000061f9  __aeabi_fcmpge                       
0000620d  __aeabi_fcmpgt                       
000061e5  __aeabi_fcmple                       
000061d1  __aeabi_fcmplt                       
00005901  __aeabi_fdiv                         
000056e1  __aeabi_fmul                         
00004931  __aeabi_fsub                         
00007809  __aeabi_i2d                          
000072fd  __aeabi_i2f                          
000067e9  __aeabi_idiv                         
000026a7  __aeabi_idiv0                        
000067e9  __aeabi_idivmod                      
0000520b  __aeabi_ldiv0                        
00007c55  __aeabi_llsl                         
00007b4d  __aeabi_lmul                         
00008775  __aeabi_memcpy                       
00008775  __aeabi_memcpy4                      
00008775  __aeabi_memcpy8                      
00008681  __aeabi_memset                       
00008681  __aeabi_memset4                      
00008681  __aeabi_memset8                      
00007b29  __aeabi_ui2d                         
00007a1d  __aeabi_ui2f                         
00006fdd  __aeabi_uidiv                        
00006fdd  __aeabi_uidivmod                     
00008599  __aeabi_uldivmod                     
00007c55  __ashldi3                            
ffffffff  __binit__                            
00005f5d  __cmpdf2                             
000073b1  __cmpsf2                             
00003c31  __divdf3                             
00005901  __divsf3                             
00005f5d  __eqdf2                              
000073b1  __eqsf2                              
0000701d  __extendsfdf2                        
00006b49  __fixdfsi                            
00007499  __fixsfsi                            
00006ed9  __fixunsdfsi                         
00007809  __floatsidf                          
000072fd  __floatsisf                          
00007b29  __floatunsidf                        
00007a1d  __floatunsisf                        
00005af9  __gedf2                              
00007339  __gesf2                              
00005af9  __gtdf2                              
00007339  __gtsf2                              
00005f5d  __ledf2                              
000073b1  __lesf2                              
00005f5d  __ltdf2                              
000073b1  __ltsf2                              
UNDEFED   __mpu_init                           
000043f9  __muldf3                             
00007b4d  __muldi3                             
000073ed  __muldsi3                            
000056e1  __mulsf3                             
00005f5d  __nedf2                              
000073b1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002515  __subdf3                             
00004931  __subsf3                             
00005b71  __truncdfsf2                         
0000520d  __udivmoddi4                         
00007a45  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000087bd  _system_pre_init                     
0000879b  abort                                
00006bdd  adc_getValue                         
000099a6  asc2_0806                            
000093b6  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002831  atan2                                
00002831  atan2l                               
00000df5  atanl                                
0000705d  atoi                                 
ffffffff  binit                                
00005e85  convertAnalogToDigital               
20200504  delayTick                            
00006c25  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00006341  dmp_enable_gyro_cal                  
00006c6d  dmp_enable_lp_quat                   
00007ea5  dmp_load_motion_driver_firmware      
00001c15  dmp_read_fifo                        
000085ad  dmp_register_android_orient_cb       
000085c1  dmp_register_tap_cb                  
00005525  dmp_set_fifo_rate                    
00002b35  dmp_set_orientation                  
00006dc9  dmp_set_shake_reject_thresh          
000075a1  dmp_set_shake_reject_time            
000075d3  dmp_set_shake_reject_timeout         
0000602b  dmp_set_tap_axes                     
00006e0d  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
00007725  dmp_set_tap_time                     
00007755  dmp_set_tap_time_multi               
20200513  enable_group1_irq                    
0000651d  frexp                                
0000651d  frexpl                               
00009e48  hw                                   
00000000  interruptVectors                     
00004859  ldexp                                
00004859  ldexpl                               
00007bf5  main                                 
00007b71  memccpy                              
00007c15  memcmp                               
202003de  more                                 
000063a1  mpu6050_i2c_sda_unlock               
00004d35  mpu_configure_fifo                   
00005be5  mpu_get_accel_fsr                    
00006401  mpu_get_gyro_fsr                     
0000756d  mpu_get_sample_rate                  
0000368d  mpu_init                             
000038d9  mpu_load_firmware                    
00003f49  mpu_lp_accel_mode                    
00003d3d  mpu_read_fifo_stream                 
00005009  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
000044dd  mpu_set_accel_fsr                    
00002375  mpu_set_bypass                       
00004df1  mpu_set_dmp_state                    
00004bad  mpu_set_gyro_fsr                     
000053f1  mpu_set_int_latched                  
00004add  mpu_set_lpf                          
00004229  mpu_set_sample_rate                  
00003435  mpu_set_sensors                      
000050b5  mpu_write_mem                        
0000309d  mspm0_i2c_read                       
00004c71  mspm0_i2c_write                      
00005161  normalizeAnalogValues                
000031d1  qsort                                
202003a0  quat                                 
00009d87  reg                                  
00004859  scalbn                               
00004859  scalbnl                              
202003d8  sensor_timestamp                     
202003dc  sensors                              
00002cad  sqrt                                 
00002cad  sqrtl                                
00009d40  test                                 
20200508  uwTick                               
0000709d  vsnprintf                            
00007835  vsprintf                             
00008661  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  dmp_read_fifo                        
00001e09  SYSCFG_DL_GPIO_init                  
000021c5  Task_Start                           
00002375  mpu_set_bypass                       
00002515  __aeabi_dsub                         
00002515  __subdf3                             
0000251f  __adddf3                             
0000251f  __aeabi_dadd                         
000026a7  __aeabi_idiv0                        
000026a9  No_MCU_Ganv_Sensor_Init              
00002831  atan2                                
00002831  atan2l                               
000029b9  OLED_Init_NonBlocking                
00002b35  dmp_set_orientation                  
00002cad  sqrt                                 
00002cad  sqrtl                                
00002e1d  MPU6050_Init                         
0000309d  mspm0_i2c_read                       
000031d1  qsort                                
00003305  OLED_ShowChar                        
00003435  mpu_set_sensors                      
00003565  Task_Tracker                         
0000368d  mpu_init                             
000037b5  PID_IQ_Prosc                         
000038d9  mpu_load_firmware                    
00003b1d  Task_Init                            
00003c31  __aeabi_ddiv                         
00003c31  __divdf3                             
00003d3d  mpu_read_fifo_stream                 
00003e45  DL_Timer_initFourCCPWMMode           
00003f49  mpu_lp_accel_mode                    
00004139  Task_Motor_PID                       
00004229  mpu_set_sample_rate                  
00004315  GROUP1_IRQHandler                    
000043f9  __aeabi_dmul                         
000043f9  __muldf3                             
000044dd  mpu_set_accel_fsr                    
000045c1  Task_OLED                            
000046a1  DL_SYSCTL_configSYSPLL               
0000477d  Get_Analog_value                     
00004859  ldexp                                
00004859  ldexpl                               
00004859  scalbn                               
00004859  scalbnl                              
00004931  __aeabi_fsub                         
00004931  __subsf3                             
0000493b  __addsf3                             
0000493b  __aeabi_fadd                         
00004add  mpu_set_lpf                          
00004bad  mpu_set_gyro_fsr                     
00004c71  mspm0_i2c_write                      
00004d35  mpu_configure_fifo                   
00004df1  mpu_set_dmp_state                    
00004ea9  Task_Add                             
00004f5d  Task_Serial                          
00005009  mpu_read_mem                         
000050b5  mpu_write_mem                        
00005161  normalizeAnalogValues                
0000520b  __aeabi_ldiv0                        
0000520d  __udivmoddi4                         
000052b1  Motor_SetDuty                        
00005351  SYSCFG_DL_initPower                  
000053f1  mpu_set_int_latched                  
0000548d  I2C_OLED_WR_Byte                     
00005525  dmp_set_fifo_rate                    
00005655  SYSCFG_DL_Motor_PWM_init             
000056e1  __aeabi_fmul                         
000056e1  __mulsf3                             
000057f9  SYSCFG_DL_UART0_init                 
00005901  __aeabi_fdiv                         
00005901  __divsf3                             
00005985  Motor_GetSpeed                       
00005a05  __TI_decompress_lzss                 
00005a81  NonBlockingDelay_Check               
00005af9  __gedf2                              
00005af9  __gtdf2                              
00005b6d  ADC0_IRQHandler                      
00005b6d  ADC1_IRQHandler                      
00005b6d  AES_IRQHandler                       
00005b6d  CANFD0_IRQHandler                    
00005b6d  DAC0_IRQHandler                      
00005b6d  DMA_IRQHandler                       
00005b6d  Default_Handler                      
00005b6d  GROUP0_IRQHandler                    
00005b6d  HardFault_Handler                    
00005b6d  I2C0_IRQHandler                      
00005b6d  I2C1_IRQHandler                      
00005b6d  NMI_Handler                          
00005b6d  PendSV_Handler                       
00005b6d  RTC_IRQHandler                       
00005b6d  SPI0_IRQHandler                      
00005b6d  SPI1_IRQHandler                      
00005b6d  SVC_Handler                          
00005b6d  TIMA0_IRQHandler                     
00005b6d  TIMA1_IRQHandler                     
00005b6d  TIMG0_IRQHandler                     
00005b6d  TIMG12_IRQHandler                    
00005b6d  TIMG6_IRQHandler                     
00005b6d  TIMG7_IRQHandler                     
00005b6d  TIMG8_IRQHandler                     
00005b6d  UART0_IRQHandler                     
00005b6d  UART1_IRQHandler                     
00005b6d  UART2_IRQHandler                     
00005b6d  UART3_IRQHandler                     
00005b71  __aeabi_d2f                          
00005b71  __truncdfsf2                         
00005be5  mpu_get_accel_fsr                    
00005c59  No_MCU_Ganv_Sensor_Init_Frist        
00005ccd  MyPrintf_DMA                         
00005d3d  OLED_ShowString                      
00005dad  Motor_Start                          
00005e19  Task_Delete                          
00005e85  convertAnalogToDigital               
00005ef1  I2C_OLED_Clear                       
00005f5d  __cmpdf2                             
00005f5d  __eqdf2                              
00005f5d  __ledf2                              
00005f5d  __ltdf2                              
00005f5d  __nedf2                              
0000602b  dmp_set_tap_axes                     
00006091  DL_SYSCTL_setHFCLKSourceHFXTParams   
000060f5  SYSCFG_DL_I2C_OLED_init              
00006159  __aeabi_dcmpeq                       
0000616d  __aeabi_dcmplt                       
00006181  __aeabi_dcmple                       
00006195  __aeabi_dcmpge                       
000061a9  __aeabi_dcmpgt                       
000061bd  __aeabi_fcmpeq                       
000061d1  __aeabi_fcmplt                       
000061e5  __aeabi_fcmple                       
000061f9  __aeabi_fcmpge                       
0000620d  __aeabi_fcmpgt                       
00006221  I2C_OLED_i2c_sda_unlock              
00006281  Key_Read                             
000062e1  Task_IdleFunction                    
00006341  dmp_enable_gyro_cal                  
000063a1  mpu6050_i2c_sda_unlock               
00006401  mpu_get_gyro_fsr                     
00006461  DL_I2C_fillControllerTXFIFO          
000064c1  SYSCFG_DL_SYSCTL_init                
0000651d  frexp                                
0000651d  frexpl                               
00006631  SYSCFG_DL_I2C_MPU6050_init           
00006689  Serial_Init                          
000066e1  Task_OLED_Init                       
00006739  __TI_ltoa                            
000067e9  __aeabi_idiv                         
000067e9  __aeabi_idivmod                      
000069cd  DL_DMA_initChannel                   
00006a65  OLED_Printf                          
00006ab1  SYSCFG_DL_ADC1_init                  
00006b49  __aeabi_d2iz                         
00006b49  __fixdfsi                            
00006b95  DL_UART_init                         
00006bdd  adc_getValue                         
00006c25  dmp_enable_6x_lp_quat                
00006c6d  dmp_enable_lp_quat                   
00006cfd  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006d41  PID_IQ_SetParams                     
00006d85  Task_Key                             
00006dc9  dmp_set_shake_reject_thresh          
00006e0d  dmp_set_tap_count                    
00006e95  No_Mcu_Ganv_Sensor_Task_Without_tick 
00006ed9  __aeabi_d2uiz                        
00006ed9  __fixunsdfsi                         
00006f1d  DL_ADC12_setClockConfig              
00006f5d  Interrupt_Init                       
00006f9d  Task_GraySensor                      
00006fdd  __aeabi_uidiv                        
00006fdd  __aeabi_uidivmod                     
0000701d  __aeabi_f2d                          
0000701d  __extendsfdf2                        
0000705d  atoi                                 
0000709d  vsnprintf                            
0000720d  DL_I2C_flushControllerTXFIFO         
00007285  Get_Anolog_Value                     
000072c1  I2C_OLED_Set_Pos                     
000072fd  __aeabi_i2f                          
000072fd  __floatsisf                          
00007339  __gesf2                              
00007339  __gtsf2                              
00007375  __TI_auto_init_nobinit_nopinit       
000073b1  __cmpsf2                             
000073b1  __eqsf2                              
000073b1  __lesf2                              
000073b1  __ltsf2                              
000073b1  __nesf2                              
000073ed  __muldsi3                            
00007427  Get_Normalize_For_User               
00007461  Task_LED                             
00007499  __aeabi_f2iz                         
00007499  __fixsfsi                            
0000756d  mpu_get_sample_rate                  
000075a1  dmp_set_shake_reject_time            
000075d3  dmp_set_shake_reject_timeout         
00007665  SYSCFG_DL_DMA_CH_RX_init             
00007695  _IQ24toF                             
00007725  dmp_set_tap_time                     
00007755  dmp_set_tap_time_multi               
00007785  NonBlockingDelay_Start               
000077b1  SYSCFG_DL_init                       
00007809  __aeabi_i2d                          
00007809  __floatsidf                          
00007835  vsprintf                             
00007861  PID_IQ_Init                          
000079f5  SysTick_Increasment                  
00007a1d  __aeabi_ui2f                         
00007a1d  __floatunsisf                        
00007a45  _c_int00_noargs                      
00007adf  DL_I2C_setClockConfig                
00007b29  __aeabi_ui2d                         
00007b29  __floatunsidf                        
00007b4d  __aeabi_lmul                         
00007b4d  __muldi3                             
00007b71  memccpy                              
00007bd5  Delay                                
00007bf5  main                                 
00007c15  memcmp                               
00007c55  __aeabi_llsl                         
00007c55  __ashldi3                            
00007e51  DL_Timer_setCaptCompUpdateMethod     
00007e6d  DL_Timer_setClockConfig              
00007ea5  dmp_load_motion_driver_firmware      
00008269  DL_Timer_setCaptureCompareOutCtl     
00008329  SYSCFG_DL_DMA_CH_TX_init             
00008341  _IQ24div                             
00008359  _IQ24mpy                             
00008467  SysGetTick                           
0000847d  __TI_zero_init_nomemset              
00008599  __aeabi_uldivmod                     
000085ad  dmp_register_android_orient_cb       
000085c1  dmp_register_tap_cb                  
000085e9  DL_UART_setClockConfig               
000085fb  TI_memcpy_small                      
0000860d  __TI_decompress_none                 
00008641  DL_Timer_setCaptureCompareValue      
00008651  SYSCFG_DL_SYSTICK_init               
00008661  wcslen                               
00008671  Get_Digtal_For_User                  
00008681  __aeabi_memset                       
00008681  __aeabi_memset4                      
00008681  __aeabi_memset8                      
000086ab  TI_memset_small                      
000086b9  SYSCFG_DL_DMA_init                   
000086c5  Sys_GetTick                          
000086d1  DL_Common_delayCycles                
00008765  SysTick_Handler                      
0000876d  __aeabi_errno_addr                   
00008775  __aeabi_memcpy                       
00008775  __aeabi_memcpy4                      
00008775  __aeabi_memcpy8                      
0000879b  abort                                
000087a0  C$$EXIT                              
000087a1  HOSTexit                             
000087a9  Reset_Handler                        
000087bd  _system_pre_init                     
000093b6  asc2_1608                            
000099a6  asc2_0806                            
00009bd0  __aeabi_ctype_table_                 
00009bd0  __aeabi_ctype_table_C                
00009d40  test                                 
00009d87  reg                                  
00009e48  hw                                   
00009f00  __TI_Handler_Table_Base              
00009f0c  __TI_Handler_Table_Limit             
00009f14  __TI_CINIT_Base                      
00009f24  __TI_CINIT_Limit                     
00009f24  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003bc  Data_Accel                           
202003c2  Data_Gyro                            
202003c8  Data_Pitch                           
202003cc  Data_Roll                            
202003d0  Data_Yaw                             
202003d4  ExISR_Flag                           
202003d8  sensor_timestamp                     
202003dc  sensors                              
202003de  more                                 
202003e0  Motor_Left                           
20200428  Motor_Right                          
2020049c  Gray_Anolog                          
202004ac  Gray_Normal                          
202004e3  Data_Tracker_Input                   
202004eb  Flag_LED                             
202004ec  Motor                                
202004f4  Data_MotorEncoder                    
202004f8  Data_Motor_TarSpeed                  
202004fc  Data_Tracker_Offset                  
20200500  __aeabi_errno                        
20200504  delayTick                            
20200508  uwTick                               
2020050e  Flag_MPU6050_Ready                   
2020050f  Gray_Digtal                          
20200513  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[330 symbols]
