/**
 * @file test_nonblocking_delay.c
 * @brief 非阻塞延时系统测试程序
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */

#include "SysTick.h"
#include <stdio.h>

// 模拟SysTick计数器用于测试
volatile uint32_t test_uwTick = 0;

// 重写Sys_GetTick函数用于测试
uint32_t Sys_GetTick(void) {
    return test_uwTick;
}

// 测试函数
void test_nonblocking_delay(void) {
    NonBlockingDelay_t delay1, delay2;
    
    printf("=== 非阻塞延时系统测试 ===\n");
    
    // 测试1: 基本延时功能
    printf("测试1: 基本延时功能\n");
    test_uwTick = 0;
    NonBlockingDelay_Start(&delay1, 100);  // 100ms延时
    
    printf("延时开始，当前时间: %lu\n", test_uwTick);
    printf("延时状态: %s\n", NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    // 模拟时间推进
    test_uwTick = 50;
    printf("时间推进到: %lu\n", test_uwTick);
    printf("延时状态: %s\n", NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    test_uwTick = 100;
    printf("时间推进到: %lu\n", test_uwTick);
    printf("延时状态: %s\n", NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    test_uwTick = 150;
    printf("时间推进到: %lu\n", test_uwTick);
    printf("延时状态: %s\n", NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    // 测试2: 多个延时并行
    printf("\n测试2: 多个延时并行\n");
    test_uwTick = 0;
    NonBlockingDelay_Start(&delay1, 50);   // 50ms延时
    NonBlockingDelay_Start(&delay2, 100);  // 100ms延时
    
    for (uint32_t t = 0; t <= 120; t += 10) {
        test_uwTick = t;
        printf("时间: %lu, 延时1: %s, 延时2: %s\n", 
               t,
               NonBlockingDelay_Check(&delay1) ? "完成" : "进行中",
               NonBlockingDelay_Check(&delay2) ? "完成" : "进行中");
    }
    
    // 测试3: 时钟溢出处理
    printf("\n测试3: 时钟溢出处理\n");
    test_uwTick = UINT32_MAX - 50;  // 接近溢出
    NonBlockingDelay_Start(&delay1, 100);  // 100ms延时，会跨越溢出点
    
    printf("开始时间: %lu\n", test_uwTick);
    printf("延时状态: %s\n", NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    test_uwTick = UINT32_MAX - 10;
    printf("时间: %lu, 延时状态: %s\n", test_uwTick, 
           NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    test_uwTick = 10;  // 溢出后
    printf("时间: %lu (溢出后), 延时状态: %s\n", test_uwTick, 
           NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    test_uwTick = 60;  // 应该完成了
    printf("时间: %lu (溢出后), 延时状态: %s\n", test_uwTick, 
           NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    // 测试4: 重置功能
    printf("\n测试4: 重置功能\n");
    test_uwTick = 0;
    NonBlockingDelay_Start(&delay1, 100);
    printf("延时开始，状态: %s\n", NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    NonBlockingDelay_Reset(&delay1);
    printf("延时重置后，状态: %s\n", NonBlockingDelay_Check(&delay1) ? "完成" : "进行中");
    
    printf("\n=== 测试完成 ===\n");
}

int main(void) {
    test_nonblocking_delay();
    return 0;
}
