#ifndef __SysTick_h
#define __SysTick_h

#include "SysConfig.h"

// 非阻塞延时结构体
typedef struct {
    uint32_t start_tick;  // 开始时间戳
    uint32_t delay_ms;    // 延时时间(ms)
    bool active;          // 延时是否激活
} NonBlockingDelay_t;

void SysTick_Increasment(void);
uint32_t Sys_GetTick(void);
uint32_t SysGetTick(uint32_t *timestamp);
void Delay(uint32_t xms);

// 非阻塞延时函数
void NonBlockingDelay_Start(NonBlockingDelay_t* delay, uint32_t ms);
bool NonBlockingDelay_Check(NonBlockingDelay_t* delay);
void NonBlockingDelay_Reset(NonBlockingDelay_t* delay);

extern volatile uint32_t uwTick;

#endif
